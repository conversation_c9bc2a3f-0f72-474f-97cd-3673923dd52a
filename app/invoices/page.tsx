"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { InvoiceList } from "@/components/invoices/invoice-list";
import { InvoiceForm } from "@/components/invoices/invoice-form";
import { CustomerForm } from "@/components/customers/customer-form";
import { ProductTypeForm } from "@/components/products/product-type-form";
import { ProductForm } from "@/components/products/product-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Invoice,
  InvoiceFormData,
  CreateCustomerData,
  CreateProductTypeData,
  CreateProductData,
} from "@/lib/types/database";
import {
  createInvoice,
  createCustomer,
  createProductType,
  createProduct,
} from "@/lib/services/database";
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";

type ViewMode =
  | "list"
  | "create-invoice"
  | "create-customer"
  | "create-product-type"
  | "create-product";

export default function InvoicesPage() {
  const router = useRouter();
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleCreateInvoice = async (data: InvoiceFormData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createInvoice(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Invoice created successfully!");
        setTimeout(() => {
          setViewMode("list");
          setSuccess(null);
        }, 2000);
      }
    } catch (err) {
      setError("Failed to create invoice");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateCustomer = async (data: CreateCustomerData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createCustomer(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Customer created successfully!");
        setTimeout(() => {
          setViewMode("list");
          setSuccess(null);
        }, 2000);
      }
    } catch (err) {
      setError("Failed to create customer");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProductType = async (data: CreateProductTypeData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createProductType(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Product type created successfully!");
        setTimeout(() => {
          setViewMode("list");
          setSuccess(null);
        }, 2000);
      }
    } catch (err) {
      setError("Failed to create product type");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProduct = async (data: CreateProductData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createProduct(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Product created successfully!");
        setTimeout(() => {
          setViewMode("list");
          setSuccess(null);
        }, 2000);
      }
    } catch (err) {
      setError("Failed to create product");
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewInvoice = (invoice: Invoice) => {
    router.push(`/invoices/${invoice.id}`);
  };

  const handleEditInvoice = (invoice: Invoice) => {
    router.push(`/invoices/${invoice.id}/edit`);
  };

  const renderContent = () => {
    switch (viewMode) {
      case "create-invoice":
        return (
          <InvoiceForm
            onSubmit={handleCreateInvoice}
            onCancel={() => setViewMode("list")}
            isLoading={isLoading}
          />
        );
      case "create-customer":
        return (
          <CustomerForm
            onSubmit={handleCreateCustomer}
            onCancel={() => setViewMode("list")}
            isLoading={isLoading}
          />
        );
      case "create-product-type":
        return (
          <ProductTypeForm
            onSubmit={handleCreateProductType}
            onCancel={() => setViewMode("list")}
            isLoading={isLoading}
          />
        );
      case "create-product":
        return (
          <ProductForm
            onSubmit={handleCreateProduct}
            onCancel={() => setViewMode("list")}
            isLoading={isLoading}
          />
        );
      default:
        return (
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="invoices" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="invoices">Invoices</TabsTrigger>
                    <TabsTrigger value="customers">Customers</TabsTrigger>
                    <TabsTrigger value="products">Products</TabsTrigger>
                  </TabsList>
                  <TabsContent value="invoices" className="space-y-4">
                    <Button
                      onClick={() => setViewMode("create-invoice")}
                      className="w-full"
                    >
                      Create New Invoice
                    </Button>
                  </TabsContent>
                  <TabsContent value="customers" className="space-y-4">
                    <Button
                      onClick={() => setViewMode("create-customer")}
                      className="w-full"
                    >
                      Add New Customer
                    </Button>
                  </TabsContent>
                  <TabsContent value="products" className="space-y-2">
                    <Button
                      onClick={() => setViewMode("create-product-type")}
                      className="w-full"
                    >
                      Add Product Type
                    </Button>
                    <Button
                      onClick={() => setViewMode("create-product")}
                      className="w-full"
                      variant="outline"
                    >
                      Add Product Variant
                    </Button>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Invoice List */}
            <InvoiceList
              onCreateNew={() => setViewMode("create-invoice")}
              onViewInvoice={handleViewInvoice}
              onEditInvoice={handleEditInvoice}
            />
          </div>
        );
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        {viewMode !== "list" && (
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode("list")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
        )}
        <h1 className="text-3xl font-bold">Invoice Management</h1>
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      {/* Content */}
      {renderContent()}
    </div>
  );
}
