// TypeScript types for the user profiles and related tables

export type UserType = 'creator' | 'store';

export interface Address {
  id: string;
  nickname: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state_province?: string;
  postal_code?: string;
  country: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  user_id: string;
  store_name: string;
  store_logo_url?: string;
  billing_address_id?: string;
  contact_email?: string;
  contact_phone?: string;
  user_type: UserType;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Shop {
  id: string;
  store_id: string;
  shop_nickname: string;
  address_id?: string;
  is_active: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Combined types for easier use
export interface UserProfileWithBilling extends UserProfile {
  billing_address_id?: string;
  billing_address_nickname?: string;
  billing_address_line_1?: string;
  billing_address_line_2?: string;
  billing_city?: string;
  billing_state_province?: string;
  billing_postal_code?: string;
  billing_country?: string;
}

export interface ShopWithAddress extends Shop {
  address_id?: string;
  address_nickname?: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state_province?: string;
  postal_code?: string;
  country?: string;
}

// Form data types for creating/updating
export interface CreateAddressData {
  nickname: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state_province?: string;
  postal_code?: string;
  country: string;
}

export interface UpdateUserProfileData {
  store_name?: string;
  store_logo_url?: string;
  billing_address_id?: string;
  contact_email?: string;
  contact_phone?: string;
  user_type?: UserType;
  is_active?: boolean;
}

export interface CreateShopData {
  shop_nickname: string;
  address_id?: string;
  notes?: string;
  is_active?: boolean;
}

export interface UpdateShopData {
  shop_nickname?: string;
  address_id?: string;
  notes?: string;
  is_active?: boolean;
}

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

export type UserProfileResponse = ApiResponse<UserProfileWithBilling>;
export type ShopsResponse = ApiResponse<ShopWithAddress[]>;
export type AddressResponse = ApiResponse<Address>;
export type ShopResponse = ApiResponse<ShopWithAddress>;
