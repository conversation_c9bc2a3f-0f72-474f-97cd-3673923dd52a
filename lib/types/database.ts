export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          query?: string;
          extensions?: Json;
          variables?: Json;
          operationName?: string;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      commission_rates: {
        Row: {
          active: boolean | null;
          description: string | null;
          id: string;
          payment_method: Database["public"]["Enums"]["payment_method"] | null;
          rate_percentage: number;
          sales_channel: Database["public"]["Enums"]["sales_channel"];
        };
        Insert: {
          active?: boolean | null;
          description?: string | null;
          id?: string;
          payment_method?: Database["public"]["Enums"]["payment_method"] | null;
          rate_percentage: number;
          sales_channel: Database["public"]["Enums"]["sales_channel"];
        };
        Update: {
          active?: boolean | null;
          description?: string | null;
          id?: string;
          payment_method?: Database["public"]["Enums"]["payment_method"] | null;
          rate_percentage?: number;
          sales_channel?: Database["public"]["Enums"]["sales_channel"];
        };
        Relationships: [];
      };
      customers: {
        Row: {
          city: string | null;
          commission_percentage: number | null;
          company_name: string | null;
          country: string | null;
          email: string | null;
          first_name: string | null;
          id: string;
          last_name: string | null;
          nif: string | null;
          phone: string | null;
          relationship_started_date: string | null;
          street_address: string | null;
          type: Database["public"]["Enums"]["customer_type"];
          vat_number: string | null;
          zip_code: string | null;
        };
        Insert: {
          city?: string | null;
          commission_percentage?: number | null;
          company_name?: string | null;
          country?: string | null;
          email?: string | null;
          first_name?: string | null;
          id?: string;
          last_name?: string | null;
          nif?: string | null;
          phone?: string | null;
          relationship_started_date?: string | null;
          street_address?: string | null;
          type: Database["public"]["Enums"]["customer_type"];
          vat_number?: string | null;
          zip_code?: string | null;
        };
        Update: {
          city?: string | null;
          commission_percentage?: number | null;
          company_name?: string | null;
          country?: string | null;
          email?: string | null;
          first_name?: string | null;
          id?: string;
          last_name?: string | null;
          nif?: string | null;
          phone?: string | null;
          relationship_started_date?: string | null;
          street_address?: string | null;
          type?: Database["public"]["Enums"]["customer_type"];
          vat_number?: string | null;
          zip_code?: string | null;
        };
        Relationships: [];
      };
      invoice_items: {
        Row: {
          description: string;
          id: string;
          invoice_id: string;
          line_total: number;
          product_id: string;
          quantity: number;
          unit_price: number;
        };
        Insert: {
          description: string;
          id?: string;
          invoice_id: string;
          line_total: number;
          product_id: string;
          quantity: number;
          unit_price: number;
        };
        Update: {
          description?: string;
          id?: string;
          invoice_id?: string;
          line_total?: number;
          product_id?: string;
          quantity?: number;
          unit_price?: number;
        };
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey";
            columns: ["invoice_id"];
            isOneToOne: false;
            referencedRelation: "invoices";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "invoice_items_product_id_fkey";
            columns: ["product_id"];
            isOneToOne: false;
            referencedRelation: "products";
            referencedColumns: ["id"];
          },
        ];
      };
      invoices: {
        Row: {
          commission_amount: number | null;
          commission_percentage: number | null;
          customer_id: string;
          due_date: string | null;
          id: string;
          invoice_date: string;
          invoice_number: number;
          notes: string | null;
          paid_date: string | null;
          payment_method: Database["public"]["Enums"]["payment_method"] | null;
          payment_reference: string | null;
          sales_channel: Database["public"]["Enums"]["sales_channel"];
          shipping_amount: number | null;
          status: Database["public"]["Enums"]["invoice_status"] | null;
          subtotal: number;
          total_amount: number;
          vat_amount: number;
          vat_percentage: number | null;
        };
        Insert: {
          commission_amount?: number | null;
          commission_percentage?: number | null;
          customer_id: string;
          due_date?: string | null;
          id?: string;
          invoice_date: string;
          invoice_number: number;
          notes?: string | null;
          paid_date?: string | null;
          payment_method?: Database["public"]["Enums"]["payment_method"] | null;
          payment_reference?: string | null;
          sales_channel: Database["public"]["Enums"]["sales_channel"];
          shipping_amount?: number | null;
          status?: Database["public"]["Enums"]["invoice_status"] | null;
          subtotal: number;
          total_amount: number;
          vat_amount: number;
          vat_percentage?: number | null;
        };
        Update: {
          commission_amount?: number | null;
          commission_percentage?: number | null;
          customer_id?: string;
          due_date?: string | null;
          id?: string;
          invoice_date?: string;
          invoice_number?: number;
          notes?: string | null;
          paid_date?: string | null;
          payment_method?: Database["public"]["Enums"]["payment_method"] | null;
          payment_reference?: string | null;
          sales_channel?: Database["public"]["Enums"]["sales_channel"];
          shipping_amount?: number | null;
          status?: Database["public"]["Enums"]["invoice_status"] | null;
          subtotal?: number;
          total_amount?: number;
          vat_amount?: number;
          vat_percentage?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: "invoices_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
        ];
      };
      product_types: {
        Row: {
          description: string | null;
          id: string;
          name: string;
          price: number;
          weight_grams: number | null;
        };
        Insert: {
          description?: string | null;
          id?: string;
          name: string;
          price: number;
          weight_grams?: number | null;
        };
        Update: {
          description?: string | null;
          id?: string;
          name?: string;
          price?: number;
          weight_grams?: number | null;
        };
        Relationships: [];
      };
      products: {
        Row: {
          active: boolean | null;
          color: string | null;
          id: string;
          product_type_id: string;
          scent: string | null;
          sku: string | null;
        };
        Insert: {
          active?: boolean | null;
          color?: string | null;
          id?: string;
          product_type_id: string;
          scent?: string | null;
          sku?: string | null;
        };
        Update: {
          active?: boolean | null;
          color?: string | null;
          id?: string;
          product_type_id?: string;
          scent?: string | null;
          sku?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "products_product_type_id_fkey";
            columns: ["product_type_id"];
            isOneToOne: false;
            referencedRelation: "product_types";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      customer_type: "individual" | "company";
      invoice_status: "draft" | "sent" | "paid" | "overdue" | "cancelled";
      payment_method: "cash" | "card" | "bank_transfer" | "shopify";
      sales_channel: "in_store" | "website" | "partner_store";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">;

type DefaultSchema =
  DatabaseWithoutInternals[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  } ? keyof (
      & DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
        "Tables"
      ]
      & DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
        "Views"
      ]
    )
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
} ? (
    & DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
      "Tables"
    ]
    & DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
      "Views"
    ]
  )[TableName] extends {
    Row: infer R;
  } ? R
  : never
  : DefaultSchemaTableNameOrOptions extends keyof (
    & DefaultSchema["Tables"]
    & DefaultSchema["Views"]
  ) ? (
      & DefaultSchema["Tables"]
      & DefaultSchema["Views"]
    )[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    } ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  } ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
      "Tables"
    ]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
} ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
    "Tables"
  ][TableName] extends {
    Insert: infer I;
  } ? I
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    } ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  } ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
      "Tables"
    ]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
} ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]][
    "Tables"
  ][TableName] extends {
    Update: infer U;
  } ? U
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    } ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  } ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]][
      "Enums"
    ]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
} ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][
    EnumName
  ]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  } ? keyof DatabaseWithoutInternals[
      PublicCompositeTypeNameOrOptions["schema"]
    ]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
} ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]][
    "CompositeTypes"
  ][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends
    keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      customer_type: ["individual", "company"],
      invoice_status: ["draft", "sent", "paid", "overdue", "cancelled"],
      payment_method: ["cash", "card", "bank_transfer", "shopify"],
      sales_channel: ["in_store", "website", "partner_store"],
    },
  },
} as const;

// Type aliases for easier usage
export type Customer = Tables<"customers">;
export type Invoice = Tables<"invoices">;
export type InvoiceItem = Tables<"invoice_items">;
export type Product = Tables<"products">;
export type ProductType = Tables<"product_types">;
export type CommissionRate = Tables<"commission_rates">;

// Insert types for creating new records
export type CreateCustomerData = TablesInsert<"customers">;
export type CreateInvoiceData = TablesInsert<"invoices">;
export type CreateInvoiceItemData = TablesInsert<"invoice_items">;
export type CreateProductData = TablesInsert<"products">;
export type CreateProductTypeData = TablesInsert<"product_types">;
export type CreateCommissionRateData = TablesInsert<"commission_rates">;

// Update types for updating records
export type UpdateCustomerData = TablesUpdate<"customers">;
export type UpdateInvoiceData = TablesUpdate<"invoices">;
export type UpdateInvoiceItemData = TablesUpdate<"invoice_items">;
export type UpdateProductData = TablesUpdate<"products">;
export type UpdateProductTypeData = TablesUpdate<"product_types">;
export type UpdateCommissionRateData = TablesUpdate<"commission_rates">;

// Enum types for easier usage
export type CustomerType = Enums<"customer_type">;
export type InvoiceStatus = Enums<"invoice_status">;
export type PaymentMethod = Enums<"payment_method">;
export type SalesChannel = Enums<"sales_channel">;

// Extended types for forms and API responses
export interface InvoiceFormData {
  customer_id: string;
  invoice_date: string;
  due_date?: string;
  sales_channel: SalesChannel;
  payment_method?: PaymentMethod;
  notes?: string;
  vat_percentage?: number;
  shipping_amount?: number;
  items: InvoiceItemFormData[];
}

export interface InvoiceItemFormData {
  product_id: string;
  description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  pageSize: number;
  totalPages: number;
}

// Extended types for relations
export interface ProductWithType extends Product {
  product_type: ProductType;
}

export interface InvoiceWithRelations extends Invoice {
  customer: Customer;
  invoice_items: (InvoiceItem & {
    product: ProductWithType;
  })[];
}
